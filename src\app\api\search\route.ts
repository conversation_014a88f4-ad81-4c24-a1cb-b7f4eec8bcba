import { NextRequest, NextResponse } from 'next/server'
import { getBlogPosts, getProjects } from '@/lib/firebase-server'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get('q')

    if (!query || query.trim() === '') {
      return NextResponse.json({ results: [] })
    }

    // Get all blog posts and projects
    const [blogPosts, projects] = await Promise.all([
      getBlogPosts(),
      getProjects()
    ])

    const searchTerm = query.toLowerCase()
    const results: any[] = []

    // Search blog posts
    blogPosts.forEach(post => {
      const titleMatch = post.title.toLowerCase().includes(searchTerm)
      const excerptMatch = post.excerpt.toLowerCase().includes(searchTerm)
      const categoryMatch = post.categories?.some(cat =>
        cat.toLowerCase().includes(searchTerm)
      )

      if (titleMatch || excerptMatch || categoryMatch) {
        results.push({
          type: 'blog',
          title: post.title,
          excerpt: post.excerpt,
          slug: post.slug,
          date: post.created_at,
          categories: post.categories
        })
      }
    })

    // Search projects
    projects.forEach(project => {
      const titleMatch = project.title.toLowerCase().includes(searchTerm)
      const descriptionMatch = project.description?.toLowerCase().includes(searchTerm)
      const techMatch = project.tech_stack?.some(tech =>
        tech.toLowerCase().includes(searchTerm)
      )

      if (titleMatch || descriptionMatch || techMatch) {
        results.push({
          type: 'project',
          title: project.title,
          excerpt: project.description || '',
          slug: project.slug,
          date: project.project_date || project.created_at,
          technologies: project.tech_stack
        })
      }
    })

    // Sort by relevance (title matches first, then by date)
    const sortedResults = results.sort((a, b) => {
      const aTitle = a.title.toLowerCase().includes(searchTerm)
      const bTitle = b.title.toLowerCase().includes(searchTerm)
      
      if (aTitle && !bTitle) return -1
      if (!aTitle && bTitle) return 1
      
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })

    return NextResponse.json({ 
      results: sortedResults,
      query: query,
      total: sortedResults.length
    })

  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      { error: 'Failed to perform search' },
      { status: 500 }
    )
  }
}
