export interface BlogPost {
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  featuredImage: string;
  content: string;
  readTime: number;
  tags?: string[];
  author?: string;
  categories?: string[];
  views?: number;
}

export interface Project {
  slug: string;
  title: string;
  description: string;
  featuredImage: string;
  images: string[];
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  content: string;
  date: string;
  category: string;
  client?: string;
  industry?: string;
  challenge?: string;
  solution?: string;
  strategy?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  consent: boolean;
}

export interface ProjectInquiryData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  website?: string;
  title: string;
  service: string;
  description: string;
  budget: string;
  deadline: string;
  hearAbout: string;
}

export interface TableOfContentsItem {
  id: string;
  title: string;
  level: number;
}

export interface NewsletterSubscription {
  email: string;
  firstName?: string;
  lastName?: string;
}

export interface NewsletterResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Database types for Firebase
export interface DatabaseBlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image?: string;
  published: boolean;
  scheduled_for?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  tags?: string[];
  categories?: string[];
  reading_time?: number;
}

export interface DatabaseProject {
  id: string;
  title: string;
  slug: string;
  content: string;
  description: string;
  featured_image?: string;
  published: boolean;
  scheduled_for?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  tags?: string[];
  project_url?: string;
  github_url?: string;
  tech_stack?: string[];
  // Project metadata fields
  client?: string;
  industry?: string;
  project_date?: string;
  technology_used?: string[];
  challenge?: string;
  solution?: string;
}

export interface UploadedFile {
  id: string;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_by: string;
  created_at: string;
  download_url: string;
}

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  website?: string;
  created_at: string;
  updated_at: string;
}

// Comment types for Firebase
export interface DatabaseComment {
  id: string;
  post_id: string;           // Links to blog post
  user_id: string;           // Commenter's Firebase UID
  user_name: string;         // Display name from social login
  user_avatar: string;       // Profile picture from social provider
  user_email: string;        // Email from social login
  content: string;           // Comment text
  status: 'pending' | 'approved' | 'spam' | 'deleted';
  created_at: string;
  updated_at: string;
  parent_id?: string;        // For reply threading (optional)
  reply_level: number;       // 0 = main comment, 1 = first reply, 2 = second reply (max 2)
  hearts: string[];          // Array of user IDs who hearted this comment
  edited?: boolean;          // Whether the comment has been edited
  hearted_by_author?: boolean; // Special highlight if author hearted it
}

export interface CommentFormData {
  content: string;
}

export interface CommentWithReplies extends DatabaseComment {
  replies: CommentWithReplies[];
}
