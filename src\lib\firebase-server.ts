import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { DatabaseBlogPost, DatabaseProject } from '@/types'

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    const projectId = process.env.FIREBASE_PROJECT_ID
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL
    const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')

    if (projectId && clientEmail && privateKey) {
      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      })
    } else {
      console.warn('Firebase admin credentials not found. Some features may not work.')
    }
  } catch (error) {
    console.error('Firebase admin initialization error:', error)
  }
}

let db: any = null

try {
  db = getFirestore()
} catch (error) {
  console.warn('Firestore not available during build time')
}

// Server-side blog operations
export async function getBlogPosts(): Promise<DatabaseBlogPost[]> {
  if (!db) {
    console.warn('Firestore not initialized')
    return []
  }

  try {
    const snapshot = await db
      .collection('blog_posts')
      .where('published', '==', true)
      .orderBy('created_at', 'desc')
      .get()

    return snapshot.docs.map(doc => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseBlogPost
    })
  } catch (error) {
    console.error('Error getting blog posts (server):', error)
    return []
  }
}

export async function getBlogPostBySlug(slug: string): Promise<DatabaseBlogPost | null> {
  if (!db) {
    console.warn('Firestore not initialized')
    return null
  }

  try {
    const snapshot = await db
      .collection('blog_posts')
      .where('slug', '==', slug)
      .where('published', '==', true)
      .limit(1)
      .get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    const data = doc.data()

    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    } as DatabaseBlogPost
  } catch (error) {
    console.error('Error getting blog post by slug (server):', error)
    return null
  }
}

export async function getProjects(): Promise<DatabaseProject[]> {
  if (!db) {
    console.warn('Firestore not initialized')
    return []
  }

  try {
    const snapshot = await db
      .collection('projects')
      .where('published', '==', true)
      .orderBy('created_at', 'desc')
      .get()

    return snapshot.docs.map(doc => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseProject
    })
  } catch (error) {
    console.error('Error getting projects (server):', error)
    return []
  }
}
