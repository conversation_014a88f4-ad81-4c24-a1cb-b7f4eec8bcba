'use client'

import { useAuth } from '@/components/providers/AuthProvider'

export default function AdminUIDChecker() {
  const { user } = useAuth()

  if (!user) {
    return (
      <div className="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded z-50">
        <strong>Debug:</strong> No user logged in
      </div>
    )
  }

  return (
    <div className="fixed top-4 right-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded z-50 max-w-md">
      <div className="text-sm">
        <strong>Debug Info:</strong><br />
        <strong>Email:</strong> {user.email}<br />
        <strong>UID:</strong> {user.uid}<br />
        <strong>Display Name:</strong> {user.displayName}<br />
        <strong>Photo URL:</strong> {user.photoURL ? 'Yes' : 'No'}
      </div>
    </div>
  )
}
