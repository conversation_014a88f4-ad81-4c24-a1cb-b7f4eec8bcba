'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { getAllComments, updateCommentStatus, deleteComment, getBlogPosts } from '@/lib/firebase-operations'
import { DatabaseComment, DatabaseBlogPost } from '@/types'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  TrashIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline'

export default function CommentsPage() {
  const { user } = useAuth()
  const [comments, setComments] = useState<DatabaseComment[]>([])
  const [posts, setPosts] = useState<DatabaseBlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'spam'>('all')

  useEffect(() => {
    if (user) {
      loadData()
    }
  }, [user])

  const loadData = async () => {
    try {
      const [commentsData, postsData] = await Promise.all([
        getAllComments(user?.uid), // Admin can see all comments
        getBlogPosts(user?.uid)
      ])
      setComments(commentsData)
      setPosts(postsData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (commentId: string, status: 'approved' | 'spam' | 'deleted') => {
    setActionLoading(commentId)
    try {
      await updateCommentStatus(commentId, status)
      setComments(comments.map(comment => 
        comment.id === commentId ? { ...comment, status } : comment
      ))
    } catch (error) {
      console.error('Error updating comment status:', error)
      alert('Failed to update comment status')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (commentId: string) => {
    if (!confirm('Are you sure you want to permanently delete this comment?')) return

    setActionLoading(commentId)
    try {
      await deleteComment(commentId)
      setComments(comments.filter(comment => comment.id !== commentId))
    } catch (error) {
      console.error('Error deleting comment:', error)
      alert('Failed to delete comment')
    } finally {
      setActionLoading(null)
    }
  }

  const getPostTitle = (postId: string) => {
    const post = posts.find(p => p.id === postId)
    return post?.title || 'Unknown Post'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredComments = comments.filter(comment => {
    if (filter === 'all') return true
    return comment.status === filter
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />
      case 'pending':
        return <ClockIcon className="w-5 h-5 text-yellow-500" />
      case 'spam':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
      case 'deleted':
        return <XCircleIcon className="w-5 h-5 text-gray-500" />
      default:
        return <ClockIcon className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200'
      case 'spam':
        return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
      case 'deleted':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
            Comments
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            Moderate and manage comments on your blog posts.
          </p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'all', label: 'All Comments', count: comments.length },
            { key: 'pending', label: 'Pending', count: comments.filter(c => c.status === 'pending').length },
            { key: 'approved', label: 'Approved', count: comments.filter(c => c.status === 'approved').length },
            { key: 'spam', label: 'Spam', count: comments.filter(c => c.status === 'spam').length },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${filter === tab.key
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }
              `}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Comments List */}
      {filteredComments.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <ChatBubbleLeftIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No comments found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {filter === 'all' ? 'No comments have been submitted yet.' : `No ${filter} comments found.`}
          </p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredComments.map((comment) => (
              <div key={comment.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <img
                      src={comment.user_avatar}
                      alt={comment.user_name}
                      className="w-10 h-10 rounded-full flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {comment.user_name}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {comment.user_email}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(comment.status)}`}>
                          {getStatusIcon(comment.status)}
                          <span className="ml-1 capitalize">{comment.status}</span>
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        On: <span className="font-medium">{getPostTitle(comment.post_id)}</span>
                      </p>
                      <div className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed whitespace-pre-wrap mb-3">
                        {comment.content}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(comment.created_at)}
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                    {comment.status === 'pending' && (
                      <button
                        onClick={() => handleStatusUpdate(comment.id, 'approved')}
                        disabled={actionLoading === comment.id}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        <CheckCircleIcon className="w-4 h-4 mr-1" />
                        Approve
                      </button>
                    )}
                    
                    {comment.status !== 'spam' && (
                      <button
                        onClick={() => handleStatusUpdate(comment.id, 'spam')}
                        disabled={actionLoading === comment.id}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                      >
                        <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                        Spam
                      </button>
                    )}

                    <button
                      onClick={() => handleDelete(comment.id)}
                      disabled={actionLoading === comment.id}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      <TrashIcon className="w-4 h-4 mr-1" />
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
