'use client'

import { useState } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { createComment } from '@/lib/firebase-operations'
import { DatabaseComment } from '@/types'

interface CommentFormProps {
  postId: string
  parentId?: string
  replyLevel?: number
  replyingTo?: string
  onCommentSubmitted?: (comment: DatabaseComment) => void
  onCancel?: () => void
}

export default function CommentForm({
  postId,
  parentId,
  replyLevel = 0,
  replyingTo,
  onCommentSubmitted,
  onCancel
}: CommentFormProps) {
  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const { user } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !content.trim()) return

    setLoading(true)
    setError('')

    try {
      const commentData = {
        post_id: postId,
        user_id: user.uid,
        user_name: user.displayName || user.email?.split('@')[0] || 'Anonymous',
        user_avatar: user.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.displayName || user.email || 'User')}&background=6366f1&color=fff`,
        user_email: user.email || '',
        content: content.trim(),
        status: 'pending' as const, // Comments start as pending for moderation
        parent_id: parentId,
        reply_level: replyLevel,
        hearts: [],
      }

      const commentId = await createComment(commentData)

      // Create the full comment object for callback
      const newComment: DatabaseComment = {
        id: commentId,
        ...commentData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      onCommentSubmitted?.(newComment)
      setContent('')
    } catch (error: any) {
      console.error('Error creating comment:', error)
      setError(error.message || 'Failed to submit comment')
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return null // This component should only render when user is authenticated
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {replyingTo && (
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          Replying to <span className="font-medium">{replyingTo}</span>
        </div>
      )}

      <div className="flex items-start space-x-3">
        <img
          src={user.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.displayName || user.email || 'User')}&background=6366f1&color=fff`}
          alt={user.displayName || 'User'}
          className="w-8 h-8 rounded-full flex-shrink-0"
        />
        <div className="flex-1">
          <div className="mb-2">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {user.displayName || user.email?.split('@')[0] || 'Anonymous'}
            </span>
          </div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={parentId ? "Write a reply..." : "Write a comment..."}
            rows={parentId ? 2 : 3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
            disabled={loading}
            required
          />
        </div>
      </div>

      {error && (
        <div className="ml-13 p-3 bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded-md text-sm">
          {error}
        </div>
      )}

      <div className="ml-11 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={loading || !content.trim()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? 'Posting...' : parentId ? 'Post Reply' : 'Post Comment'}
          </button>
        </div>
      </div>
    </form>
  )
}
